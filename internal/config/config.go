package config

import (
	"fmt"
	"log/slog"
	"os"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Server ServerConfig `mapstructure:"server"`
	Log    LogConfig    `mapstructure:"log"`
	Redis  RedisConfig  `mapstructure:"redis"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	HTTPPort  int    `mapstructure:"http_port"`
	SIPIP     string `mapstructure:"sip_ip"`
	SIPPort   int    `mapstructure:"sip_port"`
	SIPID     string `mapstructure:"sip_id"`
	SIPDomain string `mapstructure:"sip_domain"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level string `mapstructure:"level"`
	Path  string `mapstructure:"path"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Addr     string `mapstructure:"addr"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

// Load 加载配置文件
func Load(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// 设置默认值
	viper.SetDefault("server.http_port", 8080)
	viper.SetDefault("server.sip_ip", "0.0.0.0")
	viper.SetDefault("server.sip_port", 5060)
	viper.SetDefault("server.sip_id", "34020000002000000001")
	viper.SetDefault("server.sip_domain", "3402000000")
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.path", "/var/log/gb-gateway.log")
	viper.SetDefault("redis.addr", "localhost:6379")
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	slog.Info("Configuration loaded successfully", "config_file", configPath)
	return &config, nil
}

// GetLogLevel 获取日志级别
func (c *Config) GetLogLevel() slog.Level {
	switch c.Log.Level {
	case "debug":
		return slog.LevelDebug
	case "info":
		return slog.LevelInfo
	case "warn":
		return slog.LevelWarn
	case "error":
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

// CreateLogger 创建日志记录器
func (c *Config) CreateLogger() *slog.Logger {
	opts := &slog.HandlerOptions{
		Level: c.GetLogLevel(),
	}

	// 在debug模式下添加源文件信息
	if c.Log.Level == "debug" {
		opts.AddSource = true
	}

	handler := slog.NewTextHandler(os.Stdout, opts)
	return slog.New(handler)
}
